package com.xiaomi.algohub.meta.lineage.viewdepdata;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicLong;

import com.xiaomi.algohub.meta.lineage.config.TalosConfig;
import com.xiaomi.growth.feature.model.config.FeatureViewConfig;
import libthrift091.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;

import com.xiaomi.infra.galaxy.rpc.thrift.Credential;
import com.xiaomi.infra.galaxy.rpc.thrift.UserType;
import com.xiaomi.infra.galaxy.talos.client.SimpleTopicAbnormalCallback;
import com.xiaomi.infra.galaxy.talos.producer.ProducerNotActiveException;
import com.xiaomi.infra.galaxy.talos.producer.TalosProducer;
import com.xiaomi.infra.galaxy.talos.producer.TalosProducerConfig;
import com.xiaomi.infra.galaxy.talos.producer.UserMessageCallback;
import com.xiaomi.infra.galaxy.talos.producer.UserMessageResult;
import com.xiaomi.infra.galaxy.talos.thrift.Message;

public class ViewDepDataTalosProducer {
    private static final Logger LOG = LoggerFactory.getLogger(ViewDepDataTalosProducer.class);

    // callback for producer success/fail to put message
    private static class MyMessageCallback implements UserMessageCallback {
        // count when success
        @Override
        public void onSuccess(UserMessageResult userMessageResult) {
            long count = successPutNumber.addAndGet(
                    userMessageResult.getMessageList().size());

            for (Message message : userMessageResult.getMessageList()) {
                LOG.info("success to put message: " + new String(message.getMessage()));
                
            }
            LOG.info("success to put message: " + count + " so far.");
        }

        // retry when failed
        @Override
        public void onError(UserMessageResult userMessageResult) {
            try {
                for (Message message : userMessageResult.getMessageList()) {
                    LOG.info("failed to put message: " + message + " we will retry to put it.");
                }
                talosProducer.addUserMessage(userMessageResult.getMessageList());
            } catch (ProducerNotActiveException e) {
                e.printStackTrace();
            }
        }
    }

    private static final AtomicLong successPutNumber = new AtomicLong(0);
    private static TalosProducer talosProducer;
    private TalosProducerConfig producerConfig;
    private Credential credential;

    public ViewDepDataTalosProducer() {
        // 从配置文件读取配置
        Properties pros = TalosConfig.getTalosProperties();
        producerConfig = new TalosProducerConfig(pros);

        // credential
        credential = new Credential();
        credential.setSecretKeyId(TalosConfig.getAccessKey())
                .setSecretKey(TalosConfig.getAccessSecret())
                .setType(UserType.DEV_XIAOMI);
    }

    public void sendDependencyData(String baseInfoYaml, String extractYaml, String appId) throws TException {
        // init producer
        talosProducer = new TalosProducer(producerConfig, credential,
                TalosConfig.getTopicName(), new SimpleTopicAbnormalCallback(),
                new MyMessageCallback());

        try {
            ViewDepDataParser parser = new ViewDepDataParser();
            String jsonString = parser.generateDependencyJson(baseInfoYaml, extractYaml, appId);

            Message message = new Message(ByteBuffer.wrap(jsonString.getBytes(StandardCharsets.UTF_8)));
            List<Message> messageList = new ArrayList<>();
            messageList.add(message);

            LOG.info("发送依赖数据JSON到Talos: {}", jsonString);
            talosProducer.addUserMessage(messageList);

            Thread.sleep(TalosConfig.getSendTimeout());
        } catch (Exception e) {
            LOG.error("发送数据到Talos失败", e);
            throw new RuntimeException(e);
        }
    }

    public void sendDependencyData(FeatureViewConfig baseConfig, FeatureViewConfig extractConfig, String appId) throws TException {
        // init producer
        talosProducer = new TalosProducer(producerConfig, credential,
                TalosConfig.getTopicName(), new SimpleTopicAbnormalCallback(),
                new MyMessageCallback());

        try {
            ViewDepDataParser parser = new ViewDepDataParser();
            String jsonString = parser.generateDependencyJson(baseConfig, extractConfig, appId);

            Message message = new Message(ByteBuffer.wrap(jsonString.getBytes(StandardCharsets.UTF_8)));
            List<Message> messageList = new ArrayList<>();
            messageList.add(message);

            LOG.info("发送依赖数据JSON到Talos: {}", jsonString);
            talosProducer.addUserMessage(messageList);

            Thread.sleep(TalosConfig.getSendTimeout());
        } catch (Exception e) {
            LOG.error("发送数据到Talos失败", e);
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) throws Exception {
        ViewDepDataTalosProducer producerDemo = new ViewDepDataTalosProducer();
        producerDemo.sendDependencyData("featview-test-baseinfo.yaml", "feaview_u_djy_common_profile.yaml", "test_app_id");
    }
}
