package com.xiaomi.algohub.meta.lineage.config;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * Talos配置管理类
 * 支持环境特定配置文件加载
 */
public class TalosConfig {
    private static final String DEFAULT_CONFIG_FILE = "talos-config.properties";
    private static Properties properties;
    
    static {
        loadConfig();
    }
    
    private static void loadConfig() {
        // 优先读取环境特定的配置文件
        String env = System.getProperty("env", "dev");
        String configFile = "talos-config-" + env + ".properties";
        
        properties = new Properties();
        
        // 先尝试加载环境特定的配置文件
        InputStream inputStream = TalosConfig.class.getClassLoader()
                .getResourceAsStream(configFile);
        
        // 如果环境特定配置文件不存在，则使用默认配置文件
        if (inputStream == null) {
            inputStream = TalosConfig.class.getClassLoader()
                    .getResourceAsStream(DEFAULT_CONFIG_FILE);
            configFile = DEFAULT_CONFIG_FILE;
        }
        
        if (inputStream == null) {
            throw new RuntimeException("配置文件未找到: " + configFile + " 和 " + DEFAULT_CONFIG_FILE);
        }
        
        try {
            properties.load(inputStream);
            System.out.println("已加载Talos配置文件: " + configFile);
        } catch (IOException e) {
            throw new RuntimeException("加载配置文件失败: " + configFile, e);
        } finally {
            try {
                inputStream.close();
            } catch (IOException e) {
                // 忽略关闭异常
            }
        }
    }
    
    /**
     * 获取访问密钥
     */
    public static String getAccessKey() {
        return getProperty("talos.access.key");
    }
    
    /**
     * 获取访问密钥Secret
     */
    public static String getAccessSecret() {
        return getProperty("talos.access.secret");
    }
    
    /**
     * 获取服务端点
     */
    public static String getServiceEndpoint() {
        return getProperty("talos.service.endpoint");
    }
    
    /**
     * 获取Topic名称
     */
    public static String getTopicName() {
        return getProperty("talos.topic.name");
    }
    
    /**
     * 获取生产者超时时间
     */
    public static long getProducerTimeout() {
        return Long.parseLong(getProperty("talos.producer.timeout", "10000"));
    }
    
    /**
     * 获取发送超时时间
     */
    public static long getSendTimeout() {
        return Long.parseLong(getProperty("talos.send.timeout", "5000"));
    }
    
    /**
     * 获取配置属性
     */
    private static String getProperty(String key) {
        String value = properties.getProperty(key);
        if (value == null || value.trim().isEmpty()) {
            throw new RuntimeException("配置项 " + key + " 未找到或为空");
        }
        return value.trim();
    }
    
    /**
     * 获取配置属性（带默认值）
     */
    private static String getProperty(String key, String defaultValue) {
        String value = properties.getProperty(key, defaultValue);
        return value.trim();
    }
    
    /**
     * 获取所有Talos相关的Properties对象
     */
    public static Properties getTalosProperties() {
        Properties talosProps = new Properties();
        talosProps.setProperty("galaxy.talos.service.endpoint", getServiceEndpoint());
        return talosProps;
    }
    
    /**
     * 重新加载配置文件
     */
    public static void reload() {
        loadConfig();
    }
}
