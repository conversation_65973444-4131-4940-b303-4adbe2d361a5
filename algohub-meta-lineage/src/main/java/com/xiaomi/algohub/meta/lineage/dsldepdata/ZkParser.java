package com.xiaomi.algohub.meta.lineage.dsldepdata;

import com.xiaomi.data.recommender.dnnfeatures.DNNFeatureExtCpp;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.CuratorFrameworkFactory;
import org.apache.curator.retry.ExponentialBackoffRetry;
import org.apache.zookeeper.KeeperException;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.FileOutputStream;
import java.io.PrintStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

import static com.xiaomi.algohub.meta.lineage.dsldepdata.HdfsInteractor.downloadAndExtractConfigs;


public class ZkParser {

    /**
     * 连接zk
     * @param connectString
     * @return
     */
    public static CuratorFramework connect(String connectString) {
        //连接zk
        CuratorFramework client = CuratorFrameworkFactory.builder()
                .connectString(connectString.trim())
                .retryPolicy(new ExponentialBackoffRetry(1000, 3))
                .sessionTimeoutMs(30_000)
                .connectionTimeoutMs(5_000)
                .build();
        client.start();
        
        try {
            boolean ok = client.blockUntilConnected(15, java.util.concurrent.TimeUnit.SECONDS);
            if (!ok) {
              throw new RuntimeException(String.format("连接 ZK 超时: %s", connectString));
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException(String.format("等待连接被中断: %s", connectString), e);
        }
        return client;
    }


    /**
     * 读取zk节点数据
     * @param client
     * @param path
     * @return
     */
    public static String readDataAsString(CuratorFramework client, String path) {
        if (client == null) {
            throw new IllegalArgumentException("client 不能为空");
        }
        if (path == null || path.trim().isEmpty()) {
            throw new IllegalArgumentException("path 不能为空");
        }
        try {
            byte[] bytes = client.getData().forPath(path.trim());
            return new String(bytes, StandardCharsets.UTF_8);
        } catch (KeeperException.NoNodeException e) {
            throw new RuntimeException(String.format("ZK 节点不存在: %s", path), e);
        } catch (Exception e) {
            throw new RuntimeException(String.format("读取 ZK 节点失败: %s", path), e);
        }
    }

    /**
     * 解析xml数据
     * @param xml
     * @return
     */
    public static List<Map<String, String>> parsePropertyGroups(String xml) {
        if (xml == null || xml.trim().isEmpty()) {
            throw new IllegalArgumentException("xml 内容不能为空");
        }
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(false);
            factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            factory.setExpandEntityReferences(false);

            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new java.io.ByteArrayInputStream(xml.getBytes(StandardCharsets.UTF_8)));
            doc.getDocumentElement().normalize();

            NodeList groups = doc.getElementsByTagName("propertyGroup");
            return IntStream.range(0, groups.getLength())
                    .mapToObj(groups::item)
                    .filter(Objects::nonNull)
                    .filter(node -> node.getNodeType() == Node.ELEMENT_NODE)
                    .map(node -> (Element) node)
                    .map(ZkParser::elementToMap)
                    .collect(Collectors.toList());
        } catch (org.xml.sax.SAXParseException e) {
            throw new RuntimeException(String.format("XML 解析错误(行:%d, 列:%d): %s",
                    e.getLineNumber(), e.getColumnNumber(), e.getMessage()), e);
        } catch (Exception e) {
            throw new RuntimeException(String.format("XML 解析失败: %s", e.getMessage()), e);
        }
    }

    /**
     * 将xml节点转换为map
     * @param groupElement
     * @return
     */
    private static Map<String, String> elementToMap(Element groupElement) {
        Map<String, String> groupMap = new LinkedHashMap<>();
        NodeList childNodes = groupElement.getChildNodes();
        IntStream.range(0, childNodes.getLength())
                .mapToObj(childNodes::item)
                .filter(Objects::nonNull)
                .filter(n -> n.getNodeType() == Node.ELEMENT_NODE)
                .map(n -> (Element) n)
                .forEach(el -> {
                    String key = el.getTagName();
                    String value = el.getTextContent() == null ? "" : el.getTextContent().trim();
                    groupMap.put(key, value);
                });
        return groupMap;
    }

    /**
     * 获取并解析xml数据
     * @param connectString
     * @param xmlPath
     * @return
     */
    public static List<Map<String, String>> fetchAndParse(String connectString, String xmlPath) {
        CuratorFramework client = null;
        try {
            client = connect(connectString);
            String xml = readDataAsString(client, xmlPath);
            return parsePropertyGroups(xml);
        } finally {
            if (client != null) {
                client.close();
            }
        }
    }


    /**
     * 将模型字典转换为配置文件
     * @param modelMap 模型配置映射
     * @param outputPath 输出文件路径
     */
    public static void dictToConfFile(Map<String, String> modelMap, String outputPath) {
        if (modelMap == null) {
            throw new IllegalArgumentException("输入参数不能为空");
        }

        try {
            // 确保输出目录存在
            java.nio.file.Path parentDir = Paths.get(outputPath).getParent();
            if (parentDir != null && !Files.exists(parentDir)) {
                Files.createDirectories(parentDir);
            }

            String dslConfName = modelMap.getOrDefault("dslConfName", "feature.dsl");
            String dslConfPath = dslConfName.contains(":") ? dslConfName.split(":")[1] : dslConfName;

            String[] confLines = {
                    "--so_path=/home/<USER>/yusirui/extract_feature/so_file/libfeature-spark-new.so",
                    String.format("--dsl_conf=/home/<USER>/yusirui/extract_feature/tar_conf/%s", dslConfPath),
                    String.format("--feature_group_conf=/home/<USER>/yusirui/extract_feature/feature_group/%s",
                            modelMap.getOrDefault("groupFea", "feature_group.conf")),
                    String.format("--saving_file_path=/home/<USER>/yusirui/extract_feature/dependency_data/modelId_%s.log",
                            modelMap.getOrDefault("modelId", "")),
                    String.format("--model_ids=%s", modelMap.getOrDefault("modelId", ""))
            };

            try (FileWriter writer = new FileWriter(outputPath)) {
                for (String line : confLines) {
                    writer.write(line + "\n");
                }
                System.out.println(String.format("配置文件已写入：%s", outputPath));
            }

        } catch (IOException e) {
            System.err.println(String.format("写入配置文件失败: %s", e.getMessage()));
            throw new RuntimeException(e);
        }
    }

    /**
     * 批量生成配置文件
     * @param propertyGroupList 属性组列表
     * @param outputDir 输出目录
     */
    public static void generateConfFiles(List<Map<String, String>> propertyGroupList, String outputDir) {
        if (propertyGroupList == null || propertyGroupList.isEmpty()) {
            System.out.println("属性组列表为空，无需生成配置文件");
            return;
        }

        for (Map<String, String> modelMap : propertyGroupList) {
            String modelId = modelMap.getOrDefault("modelId", "unknown");
            String outputPath = String.format("%s/modelId_%s.conf", outputDir, modelId);
            dictToConfFile(modelMap, outputPath);
        }
    }

    /**
     * 执行特征依赖图提取
     * @param confFilePath 配置文件路径
     * @return 是否执行成功
     */
    public static boolean executeFeatureExtraction(String confFilePath) {
        try {
            // 创建临时文件来捕获输出
            String tempFile = "temp_output_" + System.currentTimeMillis() + ".log";

            // 重定向输出到文件
            PrintStream originalOut = System.out;
            PrintStream originalErr = System.err;
            PrintStream fileOut = new PrintStream(new FileOutputStream(tempFile));

            System.setOut(fileOut);
            System.setErr(fileOut);

            try {
                String[] args = {confFilePath};
                DNNFeatureExtCpp.main(args);
            } finally {
                System.setOut(originalOut);
                System.setErr(originalErr);
                fileOut.close();
            }

            // 读取输出文件检查结果
            String output = new String(Files.readAllBytes(Paths.get(tempFile)));
            Files.delete(Paths.get(tempFile));

            // 检查是否有失败标识
            boolean hasFailed = output.contains("DSL initialization failed") ||
                    output.contains("initialization failed") ||
                    output.contains("does not exist!");

            // 检查是否有成功标识
            boolean hasSuccess = output.contains("Feature dependencies graphs written to:");

            if (hasFailed) {
                System.err.println("特征提取失败：");
                System.err.println(output);
                return false;
            }

            if (hasSuccess) {
                System.out.println(output);
                return true;
            }

            // 默认情况下打印输出并返回false
            System.out.println(output);
            return false;

        } catch (Exception e) {
            System.err.println(String.format("执行特征提取失败: %s", e.getMessage()));
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 批量执行特征依赖图提取
     * @param confDir 配置文件目录
     */
    /**
     * 批量执行特征依赖图提取
     * @param confDir 配置文件目录
     */
    public static void batchExecuteFeatureExtraction(String confDir) {
        int successCount = 0;
        int failureCount = 0;

        try {
            java.nio.file.Path dirPath = Paths.get(confDir);
            if (!Files.exists(dirPath)) {
                System.err.println(String.format("配置文件目录不存在: %s", confDir));
                return;
            }

            List<java.nio.file.Path> confFiles = Files.list(dirPath)
                    .filter(path -> path.toString().endsWith(".conf"))
                    .collect(Collectors.toList());

            System.out.println(String.format("找到 %d 个配置文件，开始处理...", confFiles.size()));

            for (java.nio.file.Path confFile : confFiles) {
                boolean success = executeFeatureExtraction(confFile.toString());
                if (success) {
                    successCount++;
                    System.out.println(String.format("✓ 成功处理: %s", confFile.getFileName()));
                } else {
                    failureCount++;
                    System.err.println(String.format("✗ 处理失败: %s", confFile.getFileName()));
                }
                System.out.println("--------------------------------------------------");
            }

        } catch (IOException e) {
            System.err.println(String.format("读取配置文件目录失败: %s", e.getMessage()));
            e.printStackTrace();
        } finally {
            // 输出统计结果
            System.out.println("==================== 处理结果统计 ====================");
            System.out.println(String.format("总计: %d 个文件", successCount + failureCount));
            System.out.println(String.format("成功: %d 个", successCount));
            System.out.println(String.format("失败: %d 个", failureCount));
            System.out.println(String.format("成功率: %.2f%%",
                    successCount + failureCount > 0 ? (double)successCount / (successCount + failureCount) * 100 : 0));
            System.out.println("====================================================");
        }
    }

    /**
     * 主函数
     * @param args
     */
    public static void main(String[] args) {

        // 先下载配置文件
        if (!downloadAndExtractConfigs()) {
            System.err.println("下载配置文件失败，程序退出");
            System.exit(1);
        }


        String zkConnect = "c3srv.zk.hadoop.srv:11000";
        String[] xmlPaths = {
                "/data/services/recommend/config_data/ctr-score/model_config_app_store.xml",
                "/data/services/recommend/config_data/ctr-score/model_config_app_suggestion.xml",
                "/data/services/recommend/config_data/ctr-score/model_config_fastvideo.xml",
                "/data/services/recommend/config_data/ctr-score/model_config_fastvideo_tf2.xml",
                "/data/services/recommend/config_data/ctr-score/model_config_game.xml",
                "/data/services/recommend/config_data/ctr-score/model_config_smart_op_rec.xml",
                "/data/services/recommend/config_data/ctr-score/model_config_video_dnn.xml"
        };

        CuratorFramework client = null;
        try {
            client = connect(zkConnect);

            List<Map<String, String>> allPropertyGroups = new ArrayList<>();

            for (String xmlPath : xmlPaths) {
                try {
                    System.out.println(String.format("正在解析: %s", xmlPath));
                    List<Map<String, String>> propertyGroupList = fetchAndParse(zkConnect, xmlPath);
                    System.out.println(String.format("从 %s 解析到 %d 个 propertyGroup", xmlPath, propertyGroupList.size()));
                    allPropertyGroups.addAll(propertyGroupList);
                } catch (Exception e) {
                    System.err.println(String.format("解析 %s 失败: %s", xmlPath, e.getMessage()));
                    // 继续处理其他文件，不中断整个流程
                }
            }

            System.out.println(String.format("总共解析结果，共 %d 个 propertyGroup：", allPropertyGroups.size()));
            IntStream.range(0, allPropertyGroups.size())
                    .forEach(i -> System.out.println(String.format("[%d] %s", i, allPropertyGroups.get(i))));

            // 生成配置文件
            generateConfFiles(allPropertyGroups, "extract_conf");

            // 执行特征依赖图提取
            System.out.println("开始执行特征依赖图提取...");
            batchExecuteFeatureExtraction("extract_conf");

        } catch (Exception e) {
            e.printStackTrace();
            System.exit(1);
        } finally {
            if (client != null) {
                client.close();
            }
        }
    }
}