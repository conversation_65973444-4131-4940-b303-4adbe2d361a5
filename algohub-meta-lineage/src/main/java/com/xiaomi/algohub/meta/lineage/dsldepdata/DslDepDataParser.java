package com.xiaomi.algohub.meta.lineage.dsldepdata;
import com.xiaomi.data.recommender.dnnfeatures.DNNFeatureExtCpp;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

public class DslDepDataParser {
    /**
     * 执行特征依赖图提取
     * @param confFilePath 配置文件路径
     * @return 是否执行成功
     */
    public static boolean executeFeatureExtraction(String confFilePath) {
        try {
            DNNFeatureExtCpp featureExt = new DNNFeatureExtCpp(0);
            String[] args = {confFilePath};

            // 调用DNNFeatureExtCpp的main方法
            DNNFeatureExtCpp.main(args);
            return true;
        } catch (Exception e) {
            System.err.println(String.format("执行特征提取失败: %s", e.getMessage()));
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 批量执行特征依赖图提取
     * @param confDir 配置文件目录
     */
    public static void batchExecuteFeatureExtraction(String confDir) {
        try {
            java.nio.file.Path dirPath = Paths.get(confDir);
            if (!Files.exists(dirPath)) {
                System.err.println(String.format("配置文件目录不存在: %s", confDir));
                return;
            }

            Files.list(dirPath)
                    .filter(path -> path.toString().endsWith(".conf"))
                    .forEach(confFile -> {
                        System.out.println(String.format("正在处理配置文件: %s", confFile));
                        boolean success = executeFeatureExtraction(confFile.toString());
                        if (success) {
                            System.out.println(String.format("成功处理: %s", confFile));
                        } else {
                            System.err.println(String.format("处理失败: %s", confFile));
                        }
                    });

        } catch (IOException e) {
            System.err.println(String.format("读取配置文件目录失败: %s", e.getMessage()));
            e.printStackTrace();
        }
    }
    public static void main(String[] args) {
        System.out.println("Hello, World!");
    }
}
