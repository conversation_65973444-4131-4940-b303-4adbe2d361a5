<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <parent>
    <artifactId>algohub-meta</artifactId>
    <groupId>com.xiaomi.growth</groupId>
    <version>0.0.1-SNAPSHOT</version>
  </parent>



  <modelVersion>4.0.0</modelVersion>

  <groupId>com.xiaomi.algohub</groupId>
  <artifactId>algohub-meta-lineage</artifactId>
  <version>0.0.1-SNAPSHOT</version>

  <properties>
    <scala.version>2.11.8</scala.version>
    <scala.binary.version>2.11</scala.binary.version>
    <spark.version>2.1.0</spark.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.11.0</version>
    </dependency>
    <dependency>
      <groupId>com.xiaomi.data.recommender</groupId>
      <artifactId>push-pipeline-model</artifactId>
      <version>0.1-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.xiaomi.growth</groupId>
      <artifactId>algohub-common</artifactId>
      <version>0.0.1-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>bcpkix-jdk15on</artifactId>
          <groupId>org.bouncycastle</groupId>
        </exclusion>
          <!-- Spring Boot 启动器/测试 -->
        <exclusion><groupId>org.springframework.boot</groupId><artifactId>spring-boot-starter-test</artifactId></exclusion>
        <exclusion><groupId>org.springframework.boot</groupId><artifactId>spring-boot-starter-cache</artifactId></exclusion>
        <exclusion><groupId>org.springframework.boot</groupId><artifactId>spring-boot-starter-aop</artifactId></exclusion>
        <exclusion><groupId>org.springframework.boot</groupId><artifactId>spring-boot-starter-validation</artifactId></exclusion>
        <exclusion><groupId>org.springframework.boot</groupId><artifactId>spring-boot-starter-freemarker</artifactId></exclusion>

      </exclusions>
    </dependency>

    <!-- 添加缺失的 commons-configuration 依赖 -->
    <dependency>
      <groupId>commons-configuration</groupId>
      <artifactId>commons-configuration</artifactId>
      <version>1.10</version>
    </dependency>

    <!-- 添加 commons-compress 用于tar解压 -->
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-compress</artifactId>
      <version>1.21</version>
    </dependency>

    <!-- 完整的 Hadoop 依赖 -->
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-client</artifactId>
      <version>3.1.0-mdh3.1.1.9</version>
      <exclusions>
        <exclusion>
          <groupId>xerces</groupId>
          <artifactId>xercesImpl</artifactId>
        </exclusion>
        <exclusion>
          <groupId>xml-apis</groupId>
          <artifactId>xml-apis</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-hdfs</artifactId>
      <version>3.1.0-mdh3.1.1.9</version>
    </dependency>

    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-common</artifactId>
      <version>3.1.0-mdh3.1.1.9</version>
    </dependency>

    <dependency>
      <groupId>commons-configuration</groupId>
      <artifactId>commons-configuration</artifactId>
      <version>1.10</version>
    </dependency>

    <!-- 添加 JNI 相关依赖 -->
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
      <version>3.1.0</version>
    </dependency>
    <dependency>
      <groupId>org.tensorflow</groupId>
      <artifactId>tensorflow-hadoop</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.xiaomi</groupId>
      <artifactId>spark-prediction-lib-idl</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
      <version>4.5.13</version>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpcore</artifactId>
      <version>4.4.14</version>
    </dependency>

    <!-- Jackson Core -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
      <version>2.13.0</version>
    </dependency>
    <!-- Jackson Databind -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>2.13.0</version>
    </dependency>
    <!-- Jackson Annotations -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
      <version>2.13.0</version>
    </dependency>
    <dependency>
      <groupId>com.xiaomi.infra.galaxy</groupId>
      <artifactId>galaxy-talos-sdk</artifactId>
      <version>2.7.0.2</version>
    </dependency>
    <dependency>
      <groupId>org.scala-lang</groupId>
      <artifactId>scala-library</artifactId>
      <version>${scala.version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.spark</groupId>
      <artifactId>spark-streaming_${scala.binary.version}</artifactId>
      <version>${spark.version}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.xiaomi.cloud.streaming</groupId>
      <artifactId>com.xiaomi.cloud.streaming.platform.workspace15459.Wn45DjyCommon</artifactId>
      <version>THRIFT.1.1753081985</version>
    </dependency>
    <dependency>
      <groupId>com.xiaomi.cloud.streaming</groupId>
      <artifactId>com.xiaomi.cloud.streaming.platform.workspace15459.Wn45DjyCommonSim</artifactId>
      <version>THRIFT.2.1753175699</version>
    </dependency>
    <dependency>
        <groupId>org.apache.curator</groupId>
        <artifactId>curator-framework</artifactId>
        <version>4.3.0</version>
    </dependency>
    <dependency>
        <groupId>org.apache.zookeeper</groupId>
        <artifactId>zookeeper</artifactId>
        <version>3.5.9</version>
    </dependency>
  </dependencies>

  <distributionManagement>
    <repository>
      <id>central</id>
      <name>maven-release-virtual</name>
      <url>https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual</url>
    </repository>
    <snapshotRepository>
      <id>snapshots</id>
      <name>maven-snapshot-virtual</name>
      <url>https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual</url>
    </snapshotRepository>
  </distributionManagement>

  <build>
    <plugins>
      <!-- Maven JAR Plugin with Main Class -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <version>3.2.0</version>
        <configuration>
          <archive>
            <manifest>
              <mainClass>com.xiaomi.algohub.meta.lineage.dsldepdata.ZkParser</mainClass>
              <addClasspath>true</addClasspath>
            </manifest>
          </archive>
        </configuration>
      </plugin>

      <!-- Maven Shade Plugin for Fat JAR -->
      <!-- Maven Shade Plugin for Fat JAR -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.2.4</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <filters>
                <filter>
                  <artifact>*:*</artifact>
                  <excludes>
                    <exclude>META-INF/*.SF</exclude>
                    <exclude>META-INF/*.DSA</exclude>
                    <exclude>META-INF/*.RSA</exclude>
                  </excludes>
                </filter>
              </filters>
              <transformers>
                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                  <mainClass>com.xiaomi.algohub.meta.lineage.dsldepdata.ZkParser</mainClass>
                </transformer>
                <transformer implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer"/>
                <transformer implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                  <resource>META-INF/spring.handlers</resource>
                </transformer>
                <transformer implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                  <resource>META-INF/spring.schemas</resource>
                </transformer>
              </transformers>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <!-- Appassembler Plugin (保留原有配置) -->
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>appassembler-maven-plugin</artifactId>
        <version>1.1.1</version>
        <executions>
          <execution>
            <id>make-assembly</id>
            <phase>package</phase>
            <goals>
              <goal>assemble</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <configurationDirectory>conf</configurationDirectory>
          <configurationSourceDirectory>src/main/resources</configurationSourceDirectory>
          <copyConfigurationDirectory>true</copyConfigurationDirectory>
          <includeConfigurationDirectoryInClasspath>true</includeConfigurationDirectoryInClasspath>

          <repositoryLayout>flat</repositoryLayout>
          <repositoryName>lib</repositoryName>

          <assembleDirectory>${project.build.directory}/${project.artifactId}-${project.version}</assembleDirectory>

          <binFileExtensions>
            <unix>.sh</unix>
          </binFileExtensions>
          <platforms>
            <platform>unix</platform>
          </platforms>

          <extraJvmArguments>-Djava.security.krb5.conf=$BASEDIR/conf/krb5.conf</extraJvmArguments>

          <programs>
            <program>
              <mainClass>com.xiaomi.algohub.meta.lineage.dsldepdata.ZkParser</mainClass>
              <name>zk_parser</name>
            </program>
          </programs>
        </configuration>
      </plugin>
    </plugins>

    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-clean-plugin</artifactId>
          <version>3.1.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-resources-plugin</artifactId>
          <version>3.0.2</version>
        </plugin>
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.8.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.22.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-install-plugin</artifactId>
          <version>2.5.2</version>
        </plugin>
        <plugin>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>2.8.2</version>
        </plugin>
        <plugin>
          <artifactId>maven-site-plugin</artifactId>
          <version>3.7.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-project-info-reports-plugin</artifactId>
          <version>3.0.0</version>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

</project>