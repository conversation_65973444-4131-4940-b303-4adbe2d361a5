package com.xiaomi.data.recommender.dnnfeatures;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.json.JSONObject;
import org.json.JSONException;

/**
 * Created by t<PERSON><PERSON><PERSON> on 17/12/28.
 */
public class DNNFeatureExtCpp {

    public int modelId;

    // 初始化dsl环境接口
    public native boolean dslInit(String confFileName);

    // 初始化环境接口, confFileName 为配置文件名
    public native boolean envInit(int modelId, String confFileName);

    // 初始化环境接口, confFileName 为配置文件名, similarTagsFile 为similar tag file
    public native boolean envInit(int modelId, String confFileName, String similarTagsFile);

    // cpp 特征batch抽取接口
    public native byte[][] featureExtBatch(int modelId,
                                           byte[] feedsProfileBytes,
                                           byte[] requestContextBytes,
                                           byte[][] feedsInfoListBytes);

    // 获取特征依赖图的JSON字符串
    public native String getFeatureDependenciesGraph(int modelId);

    public DNNFeatureExtCpp(int modelId) {
        this.modelId = modelId;
    }

    public static void main(String[] args) {
        // Parse extract.conf
        String soPath = null;
        String dslConfPath = null;
        String featureGroupPathsStr = null;
        String savingFilePath = null;
        String modelIdsStr = null;

        if (args.length < 1) {
            System.out.println("arg0 must be extract.conf path");
            return;
        }

        String extractConf = args[0];
        try {
            BufferedReader reader = new BufferedReader(new FileReader(extractConf));
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (line.startsWith("#") || line.isEmpty()) {
                    continue;
                }
                if (line.startsWith("--so_path=")) {
                    soPath = line.substring(10);
                } else if (line.startsWith("--dsl_conf=")) {
                    dslConfPath = line.substring(11);
                } else if (line.startsWith("--feature_group_conf=")) {
                    featureGroupPathsStr = line.substring(21);
                } else if (line.startsWith("--saving_file_path=")) {
                    savingFilePath = line.substring(19);
                } else if (line.startsWith("--model_ids=")) {
                    modelIdsStr = line.substring(12);
                }
            }
            reader.close();
        } catch (IOException ex) {
            ex.printStackTrace();
            return;
        }

        // Validate configuration
        if (soPath == null || soPath.isEmpty()) {
            System.out.println("so_path is empty");
            return;
        }
        if (dslConfPath == null || dslConfPath.isEmpty()) {
            System.out.println("dsl_conf is empty");
            return;
        }
        if (featureGroupPathsStr == null || featureGroupPathsStr.isEmpty()) {
            System.out.println("feature_group_conf is empty");
            return;
        }
        if (savingFilePath == null || savingFilePath.isEmpty()) {
            System.out.println("saving_file_path is empty");
            return;
        }
        if (modelIdsStr == null || modelIdsStr.isEmpty()) {
            System.out.println("model_ids is empty");
            return;
        }

        List<Integer> modelIds = new ArrayList<Integer>();
        try {
            String[] modelIdArray = modelIdsStr.split(",");
            for (String id : modelIdArray) {
                id = id.trim();
                modelIds.add(Integer.parseInt(id));
            }
        } catch (NumberFormatException e) {
            System.out.println("Invalid model_ids format: " + modelIdsStr);
            e.printStackTrace();
            return;
        }

        List<String> featureGroupPaths = new ArrayList<String>();
        String[] featureGroupArray = featureGroupPathsStr.split(",");
        for (String path : featureGroupArray) {
            path = path.trim();
            if (!path.isEmpty()) {
                featureGroupPaths.add(path);
            }
        }

        if (modelIds.size() != featureGroupPaths.size()) {
            System.out.println("Error: Number of model_ids (" + modelIds.size() +
                    ") does not match number of feature_group_conf (" +
                    featureGroupPaths.size() + ")");
            return;
        }

//        System.out.println("so_path: " + soPath);
//        System.out.println("dsl_conf: " + dslConfPath);
//        System.out.println("feature_group_conf: " + featureGroupPathsStr);
//        System.out.println("saving_file_path: " + savingFilePath);
//        System.out.println("model_ids: " + modelIdsStr);

        try {
            System.load(soPath);
        } catch (UnsatisfiedLinkError e) {
            System.err.println("Failed to load shared library: " + soPath);
            e.printStackTrace();
            return;
        }


        DNNFeatureExtCpp featureExt = new DNNFeatureExtCpp(0);
        boolean dslInitRet = featureExt.dslInit(dslConfPath);
        if (!dslInitRet) {
            System.out.println("DSL initialization failed");
            return;
        }

        JSONObject allResults = new JSONObject();
        for (int i = 0; i < modelIds.size(); i++) {
            int modelId = modelIds.get(i);
            String featureGroupPath = featureGroupPaths.get(i);
            boolean envInitRet = featureExt.envInit(modelId, featureGroupPath);
            if (!envInitRet) {
                System.out.println("Environment initialization failed for model_id: " + modelId +
                        " with feature_group_conf: " + featureGroupPath);
                continue;
            }
            System.out.println("Initialization succeeded for model_id: " + modelId +
                    " with feature_group_conf: " + featureGroupPath);

            String jsonOutput = featureExt.getFeatureDependenciesGraph(modelId);
            
            if (jsonOutput == null || jsonOutput.isEmpty()) {
                System.out.println("Failed to get feature dependencies graph for model_id: " + modelId);
                continue;
            }
            
            try {
                JSONObject jsonObj = new JSONObject(jsonOutput);
                allResults.put(String.valueOf(modelId), jsonObj);
            } catch (JSONException e) {
                System.out.println("Invalid JSON output for model_id: " + modelId);
                e.printStackTrace();
                continue;
            }
        }

        if (allResults.length() > 0) {
            FileWriter writer = null;
            try {
                writer = new FileWriter(savingFilePath);
                writer.write(allResults.toString(4)); // Pretty print with 4-space indent
                System.out.println("Feature dependencies graphs written to: " + savingFilePath);
            } catch (IOException e) {
                System.err.println("Failed to write to file: " + savingFilePath);
                e.printStackTrace();
            } catch (JSONException e) {
                throw new RuntimeException(e);
            } finally {
                if (writer != null) {
                    try {
                        writer.close();
                    } catch (IOException e) {
                        System.err.println("Failed to close writer: " + e.getMessage());
                    }
                }
            }
        } else {
            System.out.println("No valid feature dependencies graphs to write");
        }
    }
}