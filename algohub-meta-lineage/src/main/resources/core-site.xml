<configuration>
    <property>
        <name>configuration.service</name>
        <value>org.apache.hadoop.fs.NameServiceConfigurationService</value>
    </property>
    <property>
        <name>configuration.service.name.team.id</name>
        <value>CL7202</value><!--这里不要改动，不要改成自己的teamId-->
    </property>
    <property>
        <name>hadoop.security.authentication</name>
        <value>kerberos</value>
    </property>
    <property>
        <name>dfs.namenode.kerberos.principal.pattern</name>
        <value>hdfs_{prc,srv,tst}/<EMAIL></value>
    </property>

    <!-- 推荐使用kinit的方式进行kerberos认证，简单方便。 但有时候出于安全考虑，
         或是相同linux用户需要多个kerberos账号时，就需要使用下面配置进行keytab登录。
         keytab登录只对当前进程有效。-->

    <property>
        <name>hadoop.client.kerberos.principal</name>
        <value><EMAIL></value>
    </property>
    <property>
        <name>hadoop.client.keytab.file</name>
        <value>s_grow.keytab</value>
    </property>

</configuration>