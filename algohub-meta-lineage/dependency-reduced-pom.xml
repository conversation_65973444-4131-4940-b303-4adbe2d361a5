<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>algohub-meta</artifactId>
    <groupId>com.xiaomi.growth</groupId>
    <version>0.0.1-SNAPSHOT</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.xiaomi.algohub</groupId>
  <artifactId>algohub-meta-lineage</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-clean-plugin</artifactId>
          <version>3.1.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-resources-plugin</artifactId>
          <version>3.0.2</version>
        </plugin>
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.8.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.22.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-install-plugin</artifactId>
          <version>2.5.2</version>
        </plugin>
        <plugin>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>2.8.2</version>
        </plugin>
        <plugin>
          <artifactId>maven-site-plugin</artifactId>
          <version>3.7.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-project-info-reports-plugin</artifactId>
          <version>3.0.0</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <version>3.2.0</version>
        <configuration>
          <archive>
            <manifest>
              <mainClass>com.xiaomi.algohub.meta.lineage.dsldepdata.ZkParser</mainClass>
              <addClasspath>true</addClasspath>
            </manifest>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.2.4</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <filters>
                <filter>
                  <artifact>*:*</artifact>
                  <excludes>
                    <exclude>META-INF/*.SF</exclude>
                    <exclude>META-INF/*.DSA</exclude>
                    <exclude>META-INF/*.RSA</exclude>
                  </excludes>
                </filter>
              </filters>
              <transformers>
                <transformer>
                  <mainClass>com.xiaomi.algohub.meta.lineage.dsldepdata.ZkParser</mainClass>
                </transformer>
                <transformer />
                <transformer>
                  <resource>META-INF/spring.handlers</resource>
                </transformer>
                <transformer>
                  <resource>META-INF/spring.schemas</resource>
                </transformer>
              </transformers>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>appassembler-maven-plugin</artifactId>
        <version>1.1.1</version>
        <executions>
          <execution>
            <id>make-assembly</id>
            <phase>package</phase>
            <goals>
              <goal>assemble</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <configurationDirectory>conf</configurationDirectory>
          <configurationSourceDirectory>src/main/resources</configurationSourceDirectory>
          <copyConfigurationDirectory>true</copyConfigurationDirectory>
          <includeConfigurationDirectoryInClasspath>true</includeConfigurationDirectoryInClasspath>
          <repositoryLayout>flat</repositoryLayout>
          <repositoryName>lib</repositoryName>
          <assembleDirectory>${project.build.directory}/${project.artifactId}-${project.version}</assembleDirectory>
          <binFileExtensions>
            <unix>.sh</unix>
          </binFileExtensions>
          <platforms>
            <platform>unix</platform>
          </platforms>
          <extraJvmArguments>-Djava.security.krb5.conf=$BASEDIR/conf/krb5.conf</extraJvmArguments>
          <programs>
            <program>
              <mainClass>com.xiaomi.algohub.meta.lineage.dsldepdata.ZkParser</mainClass>
              <name>zk_parser</name>
            </program>
          </programs>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <dependencies>
    <dependency>
      <groupId>org.apache.spark</groupId>
      <artifactId>spark-streaming_2.11</artifactId>
      <version>2.1.0</version>
      <scope>provided</scope>
    </dependency>
  </dependencies>
  <distributionManagement>
    <repository>
      <id>central</id>
      <name>maven-release-virtual</name>
      <url>https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual</url>
    </repository>
    <snapshotRepository>
      <id>snapshots</id>
      <name>maven-snapshot-virtual</name>
      <url>https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual</url>
    </snapshotRepository>
  </distributionManagement>
  <properties>
    <scala.version>2.11.8</scala.version>
    <scala.binary.version>2.11</scala.binary.version>
    <spark.version>2.1.0</spark.version>
  </properties>
</project>
