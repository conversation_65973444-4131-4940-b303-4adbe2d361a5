package com.xiaomi.algohub.meta.lineage.viewdepdata;

import com.xiaomi.growth.feature.model.config.FeatureViewConfig;
import com.xiaomi.growth.feature.model.inapi.rsp.FeatureInfoRsp;
import com.xiaomi.growth.feature.yaml.CustomYamlParser;
import com.xiaomi.growth.feature.yaml.expression.FeatureReferenceExpression;
import com.xiaomi.growth.feature.yaml.expression.SqlBlockExpression;
import com.xiaomi.growth.feature.yaml.expression.SqlExpression;
import com.xiaomi.growth.feature.yaml.sqlgen.ParsedMappingConfig;
import com.xiaomi.growth.feature.yaml.sqlgen.types.resolver.ThriftTypeResolver;
import org.junit.Test;

import java.util.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Paths;


public class ViewDepDataParser {
    public static class dependency_data {
        @JsonProperty("app_id")
        public String app_id;

        @JsonProperty("input")
        public List<String> input;

        @JsonProperty("output")
        public Output output;

        @JsonProperty("lineage")
        public List<lineage_item> lineage;

        public dependency_data() {
            this.input = new ArrayList<>();
            this.output = new Output();
            this.app_id = "";
        }
    }

    public static class Output{
        @JsonProperty("viewId")
        public Long viewId;

        @JsonProperty("viewName")
        public String viewName;

        @JsonProperty("version")
        public String version;

        @JsonProperty("viewType")
        public Integer viewType;

        @JsonProperty("featureEntity")
        public String featureEntity;
    }

    public static class lineage_item {
        @JsonProperty("viewFeature")
        public List<String> viewFeature;

        @JsonProperty("dependencyColumns")
        public List<String> dependencyColumns;

        public lineage_item() {
            this.viewFeature = new ArrayList<>();
            this.dependencyColumns = new ArrayList<>();
        }
    }

    /**
     * 生成依赖数据的JSON字符串
     * @param baseInfoYaml 基础信息YAML文件路径
     * @param extractYaml 特征提取YAML文件路径
     * @return 依赖数据的JSON字符串
     */
    public static String generateDependencyJson(String baseInfoYaml, String extractYaml, String appId) {
        dependency_data dd = new dependency_data();
        dd.app_id = appId;
        try {
            // 解析基础信息配置
            FeatureViewConfig config = CustomYamlParser.parseYamlFromResource(baseInfoYaml, FeatureViewConfig.class);

            dd.input = config.getBaseInfo().getDependencyTableName();
            dd.output.viewId = config.getBaseInfo().getViewId();
            dd.output.viewName = config.getBaseInfo().getViewName();
            dd.output.version = config.getBaseInfo().getVersion();
            dd.output.viewType = config.getBaseInfo().getViewType();
            dd.output.featureEntity = config.getBaseInfo().getFeatureEntity();

            // 解析特征视图配置
            FeatureViewConfig viewConfig = CustomYamlParser.parseYamlFromResource(extractYaml, FeatureViewConfig.class);

            List<lineage_item> lineage_list = new ArrayList<>();

            ParsedMappingConfig parsedMappingConfig = new ParsedMappingConfig(
                    viewConfig.getMappingConfig().getMapping(),
                    viewConfig.getSchema().getSerializeClassName(), new ThriftTypeResolver());

            Map<String, ParsedMappingConfig.Node> flattenMap = parsedMappingConfig.getFlattenMap();

            for (Map.Entry<String, ParsedMappingConfig.Node> entry : flattenMap.entrySet()) {
                lineage_item LI = new lineage_item();
                String featureName = entry.getKey();
                String path = entry.getValue().getPath();
                Object exp = entry.getValue().getValue();

                LI.viewFeature.add(path);

                if (exp instanceof FeatureReferenceExpression) {
                    FeatureReferenceExpression exp2 = (FeatureReferenceExpression) exp;
                    FeatureInfoRsp fis = parsedMappingConfig.getFeatureInfoMap().get(exp2.getReference());
                    String fullTableName = fis.getFeatureStoreTable();
                    String column = fis.getFeatureStoreColumn();
                    LI.dependencyColumns.add(fullTableName + "." + column);
                    lineage_list.add(LI);

                } else if (exp instanceof SqlExpression) {
                    SqlExpression exp2 = (SqlExpression) exp;
                    exp2.extractFeatureRefs().stream().forEach(ref -> {
                        FeatureInfoRsp fis = parsedMappingConfig.getFeatureInfoMap().get(ref);
                        String fullTableName = fis.getFeatureStoreTable();
                        String column = fis.getFeatureStoreColumn();
                        LI.dependencyColumns.add(fullTableName + "." + column);
                    });
                    lineage_list.add(LI);

                } else if (exp instanceof SqlBlockExpression) {
                    SqlBlockExpression exp2 = (SqlBlockExpression) exp;
                    exp2.getSqlExpressions().stream().forEach(sqlExp -> {
                        sqlExp.extractFeatureRefs().stream().forEach(ref -> {
                            FeatureInfoRsp fis = parsedMappingConfig.getFeatureInfoMap().get(ref);
                            String fullTableName = fis.getFeatureStoreTable();
                            String column = fis.getFeatureStoreColumn();
                            LI.dependencyColumns.add(fullTableName + "." + column);
                        });
                    });
                    lineage_list.add(LI);
                }
            }

            dd.lineage = lineage_list;

            // 生成JSON字符串
            ObjectMapper mapper = new ObjectMapper();
            return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(dd);

        } catch (Exception e) {
            throw new RuntimeException("生成依赖数据JSON失败", e);
        }
    }

    public static String generateDependencyJson(FeatureViewConfig baseConfig, FeatureViewConfig extractConfig,String appId) {
        dependency_data dd = new dependency_data();
        dd.app_id = appId;

        try {
            // 使用传入的基础信息配置
            dd.input = baseConfig.getBaseInfo().getDependencyTableName();
            dd.output.viewId = baseConfig.getBaseInfo().getViewId();
            dd.output.viewName = baseConfig.getBaseInfo().getViewName();
            dd.output.version = baseConfig.getBaseInfo().getVersion();
            dd.output.viewType = baseConfig.getBaseInfo().getViewType();
            dd.output.featureEntity = baseConfig.getBaseInfo().getFeatureEntity();

            List<lineage_item> lineage_list = new ArrayList<>();

            ParsedMappingConfig parsedMappingConfig = new ParsedMappingConfig(
                    extractConfig.getMappingConfig().getMapping(),
                    extractConfig.getSchema().getSerializeClassName(), new ThriftTypeResolver());

            Map<String, ParsedMappingConfig.Node> flattenMap = parsedMappingConfig.getFlattenMap();

            for (Map.Entry<String, ParsedMappingConfig.Node> entry : flattenMap.entrySet()) {
                lineage_item LI = new lineage_item();
                String featureName = entry.getKey();
                String path = entry.getValue().getPath();
                Object exp = entry.getValue().getValue();

                LI.viewFeature.add(path);

                if (exp instanceof FeatureReferenceExpression) {
                    FeatureReferenceExpression exp2 = (FeatureReferenceExpression) exp;
                    FeatureInfoRsp fis = parsedMappingConfig.getFeatureInfoMap().get(exp2.getReference());
                    String fullTableName = fis.getFeatureStoreTable();
                    String column = fis.getFeatureStoreColumn();
                    LI.dependencyColumns.add(fullTableName + "." + column);
                    lineage_list.add(LI);

                } else if (exp instanceof SqlExpression) {
                    SqlExpression exp2 = (SqlExpression) exp;
                    exp2.extractFeatureRefs().stream().forEach(ref -> {
                        FeatureInfoRsp fis = parsedMappingConfig.getFeatureInfoMap().get(ref);
                        String fullTableName = fis.getFeatureStoreTable();
                        String column = fis.getFeatureStoreColumn();
                        LI.dependencyColumns.add(fullTableName + "." + column);
                    });
                    lineage_list.add(LI);

                } else if (exp instanceof SqlBlockExpression) {
                    SqlBlockExpression exp2 = (SqlBlockExpression) exp;
                    exp2.getSqlExpressions().stream().forEach(sqlExp -> {
                        sqlExp.extractFeatureRefs().stream().forEach(ref -> {
                            FeatureInfoRsp fis = parsedMappingConfig.getFeatureInfoMap().get(ref);
                            String fullTableName = fis.getFeatureStoreTable();
                            String column = fis.getFeatureStoreColumn();
                            LI.dependencyColumns.add(fullTableName + "." + column);
                        });
                    });
                    lineage_list.add(LI);
                }
            }

            dd.lineage = lineage_list;

            // 生成JSON字符串
            ObjectMapper mapper = new ObjectMapper();
            return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(dd);

        } catch (Exception e) {
            throw new RuntimeException("生成依赖数据JSON失败", e);
        }
    }

    private void saveJsonToFile(String jsonContent, String fileName) {
        try {
            // 保存到项目根目录
            String filePath = Paths.get(fileName).toAbsolutePath().toString();

            try (FileWriter writer = new FileWriter(filePath)) {
                writer.write(jsonContent);
                System.out.println("JSON文件已保存到: " + filePath);
            }
        } catch (IOException e) {
            System.err.println("保存JSON文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testGenerateDependencyJson() {
        FeatureViewConfig config = CustomYamlParser.parseYamlFromResource("featview-test-baseinfo.yaml", FeatureViewConfig.class);
        FeatureViewConfig viewConfig = CustomYamlParser.parseYamlFromResource("feaview_u_djy_common_profile.yaml", FeatureViewConfig.class);
//        String jsonResult = parseDepData.generateDependencyJson(
//                "featview-test-baseinfo.yaml",
//                "feaview_u_djy_common_profile.yaml"
//        );
        String jsonResult = ViewDepDataParser.generateDependencyJson(config, viewConfig, "test_app_id");

        System.out.println("\n=== Generated JSON ===");
        System.out.println(jsonResult);

        saveJsonToFile(jsonResult, "output.json");
    }

}
