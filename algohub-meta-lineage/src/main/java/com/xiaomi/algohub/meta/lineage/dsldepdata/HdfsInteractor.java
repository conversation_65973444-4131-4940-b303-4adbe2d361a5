package com.xiaomi.algohub.meta.lineage.dsldepdata;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.fs.FileStatus;
import org.apache.hadoop.fs.FileUtil;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.zip.GZIPInputStream;
import java.util.jar.JarInputStream;
import java.util.jar.JarEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;

public class HdfsInteractor {

    /**
     * 使用Java HDFS API下载并解压配置文件
     * @return 是否成功
     */
    public static boolean downloadAndExtractConfigs() {
        try {
            Configuration conf = new Configuration();

            // 创建本地目录
            Files.createDirectories(Paths.get("feature_group"));
            Files.createDirectories(Paths.get("tar_conf"));

            System.out.println("开始下载配置文件...");

            // 1. 下载 feature_group 配置文件
            if (!downloadFiles(conf, "hdfs://zjyprc-hadoop/user/s_feeds/dsl_conf/feature_group", "feature_group", "*.conf")) {
                return false;
            }

            // 2. 下载 tar_conf 文件
            if (!downloadFiles(conf, "hdfs://zjyprc-hadoop/user/s_feeds/dsl_conf/tar_conf", "tar_conf", "*.tar")) {
                return false;
            }


            // 3. 解压tar文件并整理
            if (!extractAndOrganizeTarFiles()) {
                return false;
            }

            System.out.println("配置文件下载和解压完成");
            return true;

        } catch (Exception e) {
            System.err.println("下载配置文件失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 从HDFS下载指定类型的文件
     */
    private static boolean downloadFiles(Configuration conf, String hdfsDir, String localDir, String pattern) {
        try {
            Path hdfsPath = new Path(hdfsDir);
            FileSystem fs = FileSystem.get(hdfsPath.toUri(), conf);

            if (!fs.exists(hdfsPath)) {
                System.err.println("HDFS目录不存在: " + hdfsDir);
                return false;
            }

            FileStatus[] fileStatuses = fs.listStatus(hdfsPath);
            int downloadCount = 0;

            for (FileStatus status : fileStatuses) {
                if (!status.isFile()) continue;

                String fileName = status.getPath().getName();
                // 简单的通配符匹配
                if (pattern.equals("*.conf") && !fileName.endsWith(".conf")) continue;
                if (pattern.equals("*.tar") && !fileName.endsWith(".tar")) continue;

                // 下载文件
                Path localPath = new Path(localDir + "/" + fileName);
                System.out.println("下载文件: " + status.getPath() + " -> " + localPath);

                fs.copyToLocalFile(false, status.getPath(), localPath, true);
                downloadCount++;
            }

            System.out.println(String.format("从 %s 下载了 %d 个文件", hdfsDir, downloadCount));
            fs.close();
            return downloadCount > 0;

        } catch (Exception e) {
            System.err.println("下载文件失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 解压tar文件并整理目录结构
     */
    private static boolean extractAndOrganizeTarFiles() {
        try {
            java.nio.file.Path tarConfDir = Paths.get("tar_conf");

            // 遍历tar_conf目录中的所有.tar文件
            Files.list(tarConfDir)
                    .filter(path -> path.toString().endsWith(".tar"))
                    .forEach(tarFile -> {
                        try {
                            System.out.println("解压文件: " + tarFile.getFileName());

                            // 获取不带扩展名的文件名作为目录名
                            String tarFileName = tarFile.getFileName().toString();
                            String dirName = tarFileName.substring(0, tarFileName.lastIndexOf(".tar"));
                            java.nio.file.Path extractDir = tarConfDir.resolve(dirName);

                            // 创建解压目录
                            Files.createDirectories(extractDir);

                            extractTarFile(tarFile.toString(), extractDir.toString());
                        } catch (Exception e) {
                            System.err.println("解压失败: " + tarFile + ", 错误: " + e.getMessage());
                        }
                    });

            return true;

        } catch (Exception e) {
            System.err.println("解压tar文件失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 解压单个tar文件
     */
    private static void extractTarFile(String tarFilePath, String destDir) throws IOException {
        try (FileInputStream fis = new FileInputStream(tarFilePath);
             TarArchiveInputStream tis = new TarArchiveInputStream(fis)) {

            TarArchiveEntry entry;
            while ((entry = tis.getNextTarEntry()) != null) {
                if (entry.isDirectory()) continue;

                String entryName = entry.getName();
                // 只处理.conf和.thrift文件
                if (!entryName.endsWith(".conf") && !entryName.endsWith(".thrift")) continue;

                // 创建目标文件路径
                String fileName = Paths.get(entryName).getFileName().toString();
                java.nio.file.Path outputPath = Paths.get(destDir, fileName);

                // 写入文件
                try (FileOutputStream fos = new FileOutputStream(outputPath.toFile())) {
                    byte[] buffer = new byte[1024];
                    int len;
                    while ((len = tis.read(buffer)) != -1) {
                        fos.write(buffer, 0, len);
                    }
                }

                System.out.println("提取文件: " + entryName + " -> " + fileName);
            }
        }
    }



    public static void main(String[] args) {
        downloadAndExtractConfigs();
    }
}